<?php namespace Invato\Courses\Components;

use Cms\Classes\ComponentBase;
use Invato\Courses\Models\Course;
use Invato\Courses\Models\Instructor;
use Invato\Courses\Models\Location;
use JsonException;

class RenderInstructorList extends ComponentBase
{
    public $instructors;

    public function componentDetails()
    {
        return [
            'name'        => 'Instructeur lijst',
            'description' => 'Toont lijst met instructeurs'
        ];
    }

    public function defineProperties()
    {
        return [
            'groups' => [
                'title' => 'Lesgroepen',
                'description' => 'Selecteer de lesgroep',
                'default' => '[]',
                'type' => 'string',
            ],
            'locations' => [
                'title' => 'Locaties',
                'description' => 'Selecteer de locatie',
                'default' => '[]',
                'type' => 'string',
            ],
        ];
    }

    /**
     * @throws JsonException
     */
    public function onRun()
    {
        $query = Instructor::query()->whereIsActive(1);

        // handle courses selection
        $groups = json_decode($this->property('groups'), true, 512, JSON_THROW_ON_ERROR);
        if ($groups) {
            $query->whereHas('courses', function ($q) use ($groups) {
                $q->where('is_active', '=', 1);
                $q->whereIn('id', $groups);
            });
        }

        // handle locations selection
        $locations = json_decode($this->property('locations'), true, 512, JSON_THROW_ON_ERROR);
        if ($locations) {
            $query->whereHas('locations', function ($q) use ($locations) {
                $q->where('is_active', '=', 1);
                $q->whereIn('id', $locations);
            });
        }

        $this->instructors = $query->orderBy('sort_order')
            ->with('courses')
            ->get();

    }

    public function getGroupsOptions()
    {
        return Course::whereIsActive(1)->lists('title', 'id');
    }

    public function getLocationsOptions()
    {
        return Location::whereIsActive(1)->lists('title', 'id');
    }
}
