<?php namespace Invato\Courses\Components;

use Cms\Classes\ComponentBase;
use Invato\Courses\Models\Instructor;
use Invato\Courses\Models\Location;

class RenderLocationList extends ComponentBase
{
    public $locations;

    public function componentDetails()
    {
      return [
        'name'        => 'Locatie lijst',
        'description' => 'Toont lijst met locaties'
      ];
    }

    public function defineProperties()
    {
        return [
            // 'groups' => [
            //     'title' => 'Lesgroepen',
            //     'description' => 'Selecteer de lesgroep',
            //     'type' => 'checkboxList',
            // ],
            'instructors' => [
                'title' => 'Instructeurs',
                'description' => 'Selecteer de instructeur',
                'type' => 'checkboxList',
            ],
            'locations' => [
                'title' => 'Locaties',
                'description' => 'Selecteer de locatie',
                'type' => 'checkboxList',
            ],
        ];
    }

    public function onRun()
    {
        $query = Location::query()->whereIsActive(1);

        // // handle courses selection
        // $groups = json_decode($this->property('groups'), true, 512, JSON_THROW_ON_ERROR);
        // if ($groups) {
        //     $query->whereHas('groups', function ($q) use ($groups) {
        //         $q->where('is_active', '=', 1);
        //         $q->whereIn('id', $groups);
        //     });
        // }

        // handle locations selection
        $locations = json_decode($this->property('locations'), true, 512, JSON_THROW_ON_ERROR);
        if ($locations) {
            $query->whereIn('id', $locations);
        }

        // handle instructors selection
        $instructors = json_decode($this->property('instructors'), true, 512, JSON_THROW_ON_ERROR);
        if ($instructors) {
            $query->whereHas('instructors', function ($q) use ($instructors) {
                $q->where('is_active', '=', 1);
                $q->whereIn('id', $instructors);
            });
        }

        $this->locations = $query->orderBy('sort_order')
            ->get();
    }

    public function getInstructorsOptions()
    {
        return Instructor::whereIsActive(1)->select([
            'id',
            \DB::raw("CONCAT(first_name, ' ', last_name) as full")
        ])->lists('full','id');
    }

    public function getLocationsOptions()
    {
        return Location::whereIsActive(1)->lists('title', 'id');
    }
}
