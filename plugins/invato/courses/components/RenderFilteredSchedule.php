<?php namespace Invato\Courses\Components;

use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Cms\Classes\ComponentBase;
use Db;
use Invato\Courses\Models\Course;
use Invato\Courses\Models\Instructor;
use Invato\Courses\Models\Lesson;
use Invato\Courses\Models\LessonCategory;
use Invato\Courses\Models\Location;
use JsonException;

class RenderFilteredSchedule extends ComponentBase
{
    public $courses;
    public $lessons;
    public $locations;
    public $categories;
    public $monday;
    public $tuesday;
    public $wednesday;
    public $thursday;
    public $friday;
    public $saturday;
    public $sunday;
    public $week;

    public function componentDetails()
    {
        return [
            'name'        => 'Rooster met filter',
            'description' => 'Toont volledig rooster'
        ];
    }

    public function defineProperties()
    {
        return [
            'groups' => [
                'title' => 'Lesgroepen',
                'description' => 'Selecteer de lesgroep',
                'type' => 'checkboxList',
            ],
            'instructors' => [
                'title' => 'Instructeurs',
                'description' => 'Selecteer de instructeur',
                'type' => 'checkboxList',
            ],
            'locations' => [
                'title' => 'Locaties',
                'description' => 'Selecteer de locatie',
                'type' => 'checkboxList',
            ],
        ];
    }

    public function onRender()
    {
        $this->lessons = $this->getLessons();
        $this->week = $this->getWeekDates();
        $this->locations = Location::whereIsActive(1)->get();
        $this->categories = LessonCategory::all();
        $this->monday = $this->getDaySchedule(1);
        $this->tuesday = $this->getDaySchedule(2);
        $this->wednesday = $this->getDaySchedule(3);
        $this->thursday = $this->getDaySchedule(4);
        $this->friday = $this->getDaySchedule(5);
        $this->saturday = $this->getDaySchedule(6);
        $this->sunday = $this->getDaySchedule(7);
    }

    /**
     * @throws JsonException
     */
    public function getLessons()
    {
        $groups = json_decode($this->property('groups'), true, 512, JSON_THROW_ON_ERROR);
        $instructors = json_decode($this->property('instructors'), true, 512, JSON_THROW_ON_ERROR);
        $locations = json_decode($this->property('locations'), true, 512, JSON_THROW_ON_ERROR);

        $query = Lesson::query()
            ->with([
                'course',
                'lessondays.instructor',
                'lessondays.location',
                'lessondays.category',
                'lessondays.categories'
            ]);

        if ( $groups ) {
            $query->whereIn('course_id', $groups);
        }
        if ( $instructors ) {
            // Filter lessons that have lesson days with the selected instructor(s)
            $query->whereHas('lessondays', function($q) use ($instructors) {
                $q->whereIn('instructor_id', $instructors)
                  ->whereHas('instructor', function($instructorQuery) {
                      $instructorQuery->where('is_active', 1);
                  });
            });
        }
        if ( $locations ) {
            // Filter lessons that have lesson days with the selected location(s)
            $query->whereHas('lessondays', function($q) use ($locations) {
                $q->whereIn('location_id', $locations)
                  ->whereHas('location', function($locationQuery) {
                      $locationQuery->where('is_active', 1);
                  });
            });
        }

        // Only get lessons that have at least one lesson day with an active instructor
        if (!$instructors) {
            $query->whereHas('lessondays', function($q) {
                $q->whereHas('instructor', function($instructorQuery) {
                    $instructorQuery->where('is_active', 1);
                });
            });
        }

        $lesson = $query->whereIsActive(1)->get();

        return $lesson;
    }

    private function getWeekDates()
    {
        $period = CarbonPeriod::create(now()->startOfWeek(), '1 day', now()->endOfWeek());
        $week = [];
        foreach ($period as $day) {
            $week[] = $day->format('d-m-Y');
        }

        return $week;
    }

    private function getDaySchedule($day)
    {
        $dayschedule = [];
        foreach ( $this->lessons as $lesson ) {
            foreach ( $lesson->lessondays as $schedule ) {
                // Only include lesson days with active instructors
                if ( $schedule['day'] == $day && $schedule->instructor && $schedule->instructor->is_active) {
                    $startTime = Carbon::parse($schedule['start']);
                    $start = date_format($startTime, 'H:i');
                    $item = [];
                    $item['date'] = $this->week[$day-1];
                    $item['day'] = $day;
                    $item['course'] = $lesson->course;
                    $item['instructor'] = $schedule->instructor;
                    $item['location'] = $schedule->location;
                    $item['start'] = $start;
                    $item['end'] = $schedule['end'];
                    $item['excluded_dates'] = $lesson->excluded_dates;
                    $item['holiday_start'] = $lesson->holiday_start;
                    $item['holiday_end'] = $lesson->holiday_end;

                    if ( $schedule->tags ) {
                        $item['tags'] = $schedule['tags'];
                    }
                    if ( $schedule->category ) {
                        $item['category'] = $schedule->category;
                    }

                    if ( $schedule->categories ) {
                        $item['categories'] = $schedule->categories;
                    }

                    $dayschedule[] = $item;
                }
            }
        }

        return collect($dayschedule);
    }

    public function getGroupsOptions()
    {
        return Course::whereIsActive(1)->lists('title', 'id');
    }

    public function getLocationsOptions()
    {
        return Location::whereIsActive(1)->lists('title', 'id');
    }

    public function getInstructorsOptions()
    {
        return Instructor::whereIsActive(1)->select([
            'id',
            DB::raw("CONCAT(first_name, ' ', last_name) as full")
        ])->lists('full','id');
    }

}
