handle: locations
name: Locaties
section: Dans
icon: /plugins/offline/boxes/assets/img/boxes/image.svg

spacing:
- general

form:
    tabs:
        fields:
            mixin-title-subtitle:
                type: mixin
                tab: Algemeen
            content:
                type: mixin
                tab: Algemeen
            _section1:
                type: section
                label: Locatie Filter
                comment: Als er geen opties gekozen zijn, zullen alle locaties getoond worden op de website.
                tab: Algemeen
            instructors:
                label: Instructeurs
                type: checkboxlist
                options: Invato\Courses\Components\RenderLocationList::getInstructorsOptions
                tab: Algemeen
            # groups:
            #     label: Lesgroepen
            #     type: checkboxlist
            #     tab: Algemeen
            locations:
                label: Locaties
                type: checkboxlist
                options: Invato\Courses\Components\RenderLocationList::getLocationsOptions
                tab: Algemeen
            background_color:
                type: mixin
                tab: Design
            overlay:
                type: mixin
                tab: Design
            card_mode:
                label: Blokken weergave
                type: balloon-selector
                default: 'slider'
                options:
                    'slider': 'Slider'
                    'static': 'Statisch'
                tab: Design
            card_grid:
                label: Blokken in rijen van...
                type: dropdown
                default: '3'
                options:
                    '2': '2 blokken'
                    '3': '3 blokken'
                    '4': '4 blokken'
                trigger:
                    condition: value[static]
                    action: show
                    field: card_mode
                tab: Design
            anchor:
                type: mixin
                tab: Design
            custom_css:
                type: mixin
                tab: Design
