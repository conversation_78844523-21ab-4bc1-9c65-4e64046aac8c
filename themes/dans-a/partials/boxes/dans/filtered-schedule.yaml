handle: filtered-schedule
name: <PERSON><PERSON><PERSON> met filter
section: Dans
icon: /plugins/offline/boxes/assets/img/boxes/image.svg

spacing:
    - general

form:
    tabs:
        fields:
            mixin-title-subtitle:
                type: mixin
                tab: Algemeen
            content:
                type: mixin
                tab: Algemeen
            _section1:
                type: section
                label: Agenda Filter
                comment: Als er geen opties gekozen zijn, zullen alle agenda items getoond worden op de website.
                tab: Algemeen
            instructors:
                label: Instructeurs
                type: checkboxlist
                options: Invato\Courses\Components\RenderFilteredSchedule::getInstructorsOptions
                tab: Algemeen
            groups:
                label: Lesgroepen
                type: checkboxlist
                options: Invato\Courses\Components\RenderFilteredSchedule::getGroupsOptions
                tab: Algemeen
            locations:
                label: Locaties
                type: checkboxlist
                options: Invato\Courses\Components\RenderFilteredSchedule::getLocationsOptions
                tab: Algemeen
            background_color:
                type: mixin
                tab: Design
            anchor:
                type: mixin
                tab: Design
            custom_css:
                type: mixin
                tab: Design
